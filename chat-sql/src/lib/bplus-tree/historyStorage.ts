/**
 * B+树历史存储服务
 * 基于IndexedDB实现历史记录的持久化存储
 */

import {
  HistoryStep,
  HistorySession,
  HistoryStorageConfig,
  HistoryQueryOptions,
  HistoryOperationResult
} from '@/types/bPlusHistory';

// 默认存储配置
const DEFAULT_CONFIG: HistoryStorageConfig = {
  dbName: 'BPlusTreeHistory',
  dbVersion: 1,
  sessionStoreName: 'sessions',
  stepStoreName: 'steps'
};

export class BPlusHistoryStorage {
  private db: IDBDatabase | null = null;
  private initialized: Promise<void>;
  private config: HistoryStorageConfig;

  constructor(config?: Partial<HistoryStorageConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initialized = this.initialize();
  }

  private async ensureInitialized(): Promise<void> {
    await this.initialized;
  }

  /**
   * 初始化IndexedDB连接
   */
  private async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.dbVersion);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB for history storage'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建会话存储
        if (!db.objectStoreNames.contains(this.config.sessionStoreName)) {
          const sessionStore = db.createObjectStore(this.config.sessionStoreName, { keyPath: 'id' });
          sessionStore.createIndex('name', 'name', { unique: false });
          sessionStore.createIndex('createdAt', 'createdAt', { unique: false });
          sessionStore.createIndex('updatedAt', 'updatedAt', { unique: false });
          sessionStore.createIndex('order', 'order', { unique: false });
        }

        // 创建步骤存储
        if (!db.objectStoreNames.contains(this.config.stepStoreName)) {
          const stepStore = db.createObjectStore(this.config.stepStoreName, { keyPath: 'id' });
          stepStore.createIndex('sessionId', 'sessionId', { unique: false });
          stepStore.createIndex('timestamp', 'timestamp', { unique: false });
          stepStore.createIndex('operation', 'operation', { unique: false });
        }
      };
    });
  }

  /**
   * 创建新的历史会话
   */
  async createSession(name: string, order: number, initialStep?: HistoryStep): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    try {
      const sessionId = this.generateId('session');
      const now = Date.now();
      
      const session: HistorySession = {
        id: sessionId,
        name,
        order,
        steps: initialStep ? [initialStep] : [],
        currentStepIndex: initialStep ? 0 : -1,
        createdAt: now,
        updatedAt: now
      };

      await this.saveSession(session);
      
      // 如果有初始步骤，也保存它
      if (initialStep) {
        await this.saveStep(sessionId, initialStep);
      }

      return { success: true, data: session };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 保存会话
   */
  private async saveSession(session: HistorySession): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.sessionStoreName], 'readwrite');
      const store = transaction.objectStore(this.config.sessionStoreName);
      const request = store.put(session);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to save session'));
    });
  }

  /**
   * 保存步骤
   */
  private async saveStep(sessionId: string, step: HistoryStep): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stepStoreName], 'readwrite');
      const store = transaction.objectStore(this.config.stepStoreName);
      
      // 为步骤添加会话ID
      const stepWithSessionId = { ...step, sessionId };
      const request = store.put(stepWithSessionId);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to save step'));
    });
  }

  /**
   * 添加新步骤到会话
   */
  async addStep(sessionId: string, step: HistoryStep): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    try {
      // 获取会话
      const session = await this.getSession(sessionId);
      if (!session.success || !session.data) {
        return { success: false, error: 'Session not found' };
      }

      const sessionData = session.data as HistorySession;
      
      // 如果当前不在最新步骤，需要创建新分支（简化实现：直接截断后续步骤）
      if (sessionData.currentStepIndex < sessionData.steps.length - 1) {
        sessionData.steps = sessionData.steps.slice(0, sessionData.currentStepIndex + 1);
      }

      // 添加新步骤
      sessionData.steps.push(step);
      sessionData.currentStepIndex = sessionData.steps.length - 1;
      sessionData.updatedAt = Date.now();

      // 保存更新的会话和新步骤
      await this.saveSession(sessionData);
      await this.saveStep(sessionId, step);

      return { success: true, data: sessionData };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 获取会话
   */
  async getSession(sessionId: string): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    return new Promise((resolve) => {
      const transaction = this.db!.transaction([this.config.sessionStoreName], 'readonly');
      const store = transaction.objectStore(this.config.sessionStoreName);
      const request = store.get(sessionId);

      request.onsuccess = () => {
        if (request.result) {
          resolve({ success: true, data: request.result });
        } else {
          resolve({ success: false, error: 'Session not found' });
        }
      };

      request.onerror = () => {
        resolve({ success: false, error: 'Failed to get session' });
      };
    });
  }

  /**
   * 获取所有会话：支持排序、搜索和分页功能的复杂查询操作
   */
  async getAllSessions(options?: HistoryQueryOptions): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    return new Promise((resolve) => {
      const transaction = this.db!.transaction([this.config.sessionStoreName], 'readonly');
      const store = transaction.objectStore(this.config.sessionStoreName);
      
      // 根据排序选项选择索引
      const sortBy = options?.sortBy || 'updatedAt';
      const sortOrder = options?.sortOrder || 'desc';
      
      let request: IDBRequest;
      if (sortBy === 'name' || sortBy === 'createdAt' || sortBy === 'updatedAt') {
        const index = store.index(sortBy);
        request = index.openCursor(null, sortOrder === 'desc' ? 'prev' : 'next');
      } else {
        request = store.openCursor();
      }

      const sessions: HistorySession[] = [];
      let count = 0;
      const limit = options?.limit || Infinity;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor && count < limit) {
          const session = cursor.value as HistorySession;
          
          // 应用搜索过滤
          if (!options?.search || session.name.toLowerCase().includes(options.search.toLowerCase())) {
            sessions.push(session);
            count++;
          }
          
          cursor.continue();
        } else {
          resolve({ success: true, data: sessions });
        }
      };

      request.onerror = () => {
        resolve({ success: false, error: 'Failed to get sessions' });
      };
    });
  }

  /**
   * 更新会话的当前步骤索引
   */
  async updateCurrentStep(sessionId: string, stepIndex: number): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    try {
      const sessionResult = await this.getSession(sessionId);
      if (!sessionResult.success || !sessionResult.data) {
        return { success: false, error: 'Session not found' };
      }

      const session = sessionResult.data as HistorySession;
      
      if (stepIndex < 0 || stepIndex >= session.steps.length) {
        return { success: false, error: 'Invalid step index' };
      }

      session.currentStepIndex = stepIndex;
      session.updatedAt = Date.now();

      await this.saveSession(session);
      return { success: true, data: session };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    try {
      // 删除会话相关的所有步骤
      await this.deleteStepsBySession(sessionId);
      
      // 删除会话
      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([this.config.sessionStoreName], 'readwrite');
        const store = transaction.objectStore(this.config.sessionStoreName);
        const request = store.delete(sessionId);

        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error('Failed to delete session'));
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 删除会话的所有步骤
   */
  private async deleteStepsBySession(sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stepStoreName], 'readwrite');
      const store = transaction.objectStore(this.config.stepStoreName);
      const index = store.index('sessionId');
      const request = index.openCursor(IDBKeyRange.only(sessionId));

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };

      request.onerror = () => reject(new Error('Failed to delete steps'));
    });
  }

  /**
   * 重命名会话
   */
  async renameSession(sessionId: string, newName: string): Promise<HistoryOperationResult> {
    await this.ensureInitialized();
    if (!this.db) {
      return { success: false, error: 'Database not initialized' };
    }

    try {
      const sessionResult = await this.getSession(sessionId);
      if (!sessionResult.success || !sessionResult.data) {
        return { success: false, error: 'Session not found' };
      }

      const session = sessionResult.data as HistorySession;
      session.name = newName;
      session.updatedAt = Date.now();

      await this.saveSession(session);
      return { success: true, data: session };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(prefix: string = 'id'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 单例实例
let historyStorageInstance: BPlusHistoryStorage | null = null;

/**
 * 获取历史存储实例
 */
export async function getBPlusHistoryStorage(): Promise<BPlusHistoryStorage> {
  if (!historyStorageInstance) {
    historyStorageInstance = new BPlusHistoryStorage();
    await historyStorageInstance['ensureInitialized']();
  }
  return historyStorageInstance;
}
