/**
 * B+树历史记录数据结构定义
 * 支持类似Git的版本控制与回溯功能
 */

import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

/**
 * 历史步骤接口
 * 记录每个操作的完整状态快照
 */
export interface HistoryStep {
  /** 步骤唯一标识符 */
  id: string;
  
  /** 操作类型 */
  operation: 'insert' | 'delete' | 'initial' | 'reset';
  
  /** 操作的键值（如果适用） */
  key?: number;
  
  /** 操作时间戳 */
  timestamp: number;
  
  /** 节点状态快照 */
  nodes: Node<BPlusNodeData>[];
  
  /** 边状态快照 */
  edges: Edge[];
  
  /** 所有键值的有序列表 */
  keys: number[];
  
  /** 操作描述 */
  description: string;
}

/**
 * 历史会话接口
 * 包含一个完整的B+树操作序列
 */
export interface HistorySession {
  /** 会话唯一标识符 */
  id: string;
  
  /** 会话名称 */
  name: string;
  
  /** B+树阶数 */
  order: number;
  
  /** 历史步骤列表 */
  steps: HistoryStep[];
  
  /** 当前激活的步骤索引 */
  currentStepIndex: number;
  
  /** 创建时间 */
  createdAt: number;
  
  /** 最后更新时间 */
  updatedAt: number;
}

/**
 * 历史存储配置
 */
export interface HistoryStorageConfig {
  /** 数据库名称 */
  dbName: string;
  
  /** 数据库版本 */
  dbVersion: number;
  
  /** 会话存储表名 */
  sessionStoreName: string;
  
  /** 步骤存储表名 */
  stepStoreName: string;
}

/**
 * 历史查询选项
 */
export interface HistoryQueryOptions {
  /** 限制返回数量 */
  limit?: number;
  
  /** 排序字段 */
  sortBy?: 'createdAt' | 'updatedAt' | 'name';
  
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  
  /** 搜索关键词 */
  search?: string;
}

/**
 * 历史操作结果
 */
export interface HistoryOperationResult {
  /** 操作是否成功 */
  success: boolean;
  
  /** 错误信息（如果失败） */
  error?: string;
  
  /** 返回数据 */
  data?: any;
}
