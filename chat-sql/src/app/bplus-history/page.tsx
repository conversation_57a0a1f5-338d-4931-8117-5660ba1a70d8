'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Container,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';
import SessionList from '@/components/BPlusHistory/SessionList';
import StepTimeline from '@/components/BPlusHistory/StepTimeline';
import BPlusTreeVisualizer from '@/components/BPlusXyflow/BPlusTreeVisualizer';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab面板组件
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`history-tabpanel-${index}`}
      aria-labelledby={`history-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const BPlusHistoryPage: React.FC = () => {
  // 状态管理
  const [tabValue, setTabValue] = useState(0);
  const [sessions, setSessions] = useState<HistorySession[]>([]);
  const [selectedSession, setSelectedSession] = useState<HistorySession | null>(null);
  const [selectedStep, setSelectedStep] = useState<HistoryStep | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  
  // 消息提示状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // 显示消息的辅助函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // 加载所有会话
  const loadSessions = useCallback(async () => {
    try {
      setLoading(true);
      const storage = await getBPlusHistoryStorage();
      const result = await storage.getAllSessions({
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      });

      if (result.success) {
        setSessions(result.data || []);
      } else {
        setError(result.error || '加载会话失败');
        showMessage(result.error || '加载会话失败', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载会话时发生未知错误';
      setError(errorMessage);
      showMessage(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  }, [showMessage]);

  // 初始化加载
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  // 处理会话选择
  const handleSessionSelect = useCallback((session: HistorySession) => {
    setSelectedSession(session);
    // 自动选择当前步骤
    if (session.steps.length > 0 && session.currentStepIndex >= 0) {
      setSelectedStep(session.steps[session.currentStepIndex]);
    } else {
      setSelectedStep(null);
    }
    // 切换到步骤时间线标签页
    setTabValue(1);
  }, []);

  // 处理步骤选择
  const handleStepSelect = useCallback(async (step: HistoryStep, stepIndex: number) => {
    if (!selectedSession) return;

    try {
      const storage = await getBPlusHistoryStorage();
      const result = await storage.updateCurrentStep(selectedSession.id, stepIndex);

      if (result.success) {
        setSelectedStep(step);
        setSelectedSession(result.data);
        // 更新会话列表中的对应会话
        setSessions(prev => prev.map(s => 
          s.id === selectedSession.id ? result.data : s
        ));
        showMessage(`已回溯到步骤: ${step.description}`, 'success');
      } else {
        showMessage(result.error || '回溯失败', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '回溯时发生未知错误';
      showMessage(errorMessage, 'error');
    }
  }, [selectedSession, showMessage]);

  // 处理新操作（从可视化组件触发）
  const handleStateChange = useCallback(async (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => {
    if (!selectedSession || !state.operation) return;

    try {
      const storage = await getBPlusHistoryStorage();
      
      // 创建新的历史步骤
      const newStep: HistoryStep = {
        id: `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        operation: state.operation,
        key: state.operationKey,
        timestamp: Date.now(),
        nodes: state.nodes,
        edges: state.edges,
        keys: state.keys,
        description: generateStepDescription(state.operation, state.operationKey)
      };

      // 添加步骤到会话
      const result = await storage.addStep(selectedSession.id, newStep);

      if (result.success) {
        setSelectedSession(result.data);
        setSelectedStep(newStep);
        // 更新会话列表
        setSessions(prev => prev.map(s => 
          s.id === selectedSession.id ? result.data : s
        ));
        showMessage(`已记录操作: ${newStep.description}`, 'success');
      } else {
        showMessage(result.error || '记录操作失败', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '记录操作时发生未知错误';
      showMessage(errorMessage, 'error');
    }
  }, [selectedSession, showMessage]);

  // 生成步骤描述
  const generateStepDescription = (operation: string, key?: number): string => {
    switch (operation) {
      case 'insert':
        return `插入键值 ${key}`;
      case 'delete':
        return `删除键值 ${key}`;
      case 'reset':
        return '重置B+树';
      default:
        return '未知操作';
    }
  };

  // 处理Tab切换
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 关闭消息提示
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'var(--primary-text)' }}>
        B+树操作历史
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3, color: 'var(--secondary-text)' }}>
        查看和管理B+树的操作历史，支持回溯到任意历史状态
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={2} sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="历史管理标签页">
            <Tab label="历史会话" id="history-tab-0" aria-controls="history-tabpanel-0" />
            <Tab 
              label="操作步骤" 
              id="history-tab-1" 
              aria-controls="history-tabpanel-1"
              disabled={!selectedSession}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <SessionList
            sessions={sessions}
            onSessionSelect={handleSessionSelect}
            onSessionsChange={loadSessions}
            showMessage={showMessage}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {selectedSession ? (
            <Box sx={{ display: 'flex', gap: 3, height: '70vh' }}>
              {/* 左侧：步骤时间线 */}
              <Box sx={{ width: '300px', flexShrink: 0 }}>
                <StepTimeline
                  session={selectedSession}
                  selectedStep={selectedStep}
                  onStepSelect={handleStepSelect}
                />
              </Box>
              
              {/* 右侧：B+树可视化 */}
              <Box sx={{ flex: 1, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <BPlusTreeVisualizer
                  initialKeys={[]}
                  order={selectedSession.order}
                  externalNodes={selectedStep?.nodes}
                  externalEdges={selectedStep?.edges}
                  onStateChange={handleStateChange}
                />
              </Box>
            </Box>
          ) : (
            <Typography variant="body1" sx={{ color: 'var(--secondary-text)' }}>
              请先选择一个会话
            </Typography>
          )}
        </TabPanel>
      </Paper>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default BPlusHistoryPage;
