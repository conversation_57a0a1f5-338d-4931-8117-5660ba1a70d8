/**
 * B+树可视化核心组件
 * 专注于可视化功能，移除操作控件，支持外部状态控制
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider,
  Panel,
  MarkerType
} from '@xyflow/react';
import { Box, Alert, Snackbar } from '@mui/material';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';
import { BPlusTreeAlgorithm } from '@/lib/bplus-tree/algorithm';
import { AnimationManager } from '@/lib/bplus-tree/animationManager';
import { CommandExecutor } from '@/lib/bplus-tree/commandExecutor';
import BPlusInternalNode from './BPlusInternalNode';
import BPlusLeafNode from './BPlusLeafNode';
import '@xyflow/react/dist/style.css';

// 布局算法
const layoutNodes = (nodes: Node<BPlusNodeData>[], edges: Edge[]): Node<BPlusNodeData>[] => {
  if (nodes.length === 0) return nodes;

  const levelGroups: { [level: number]: Node<BPlusNodeData>[] } = {};
  nodes.forEach(node => {
    const level = node.data.level;
    if (!levelGroups[level]) levelGroups[level] = [];
    levelGroups[level].push(node);
  });

  const layoutedNodes: Node<BPlusNodeData>[] = [];
  const levels = Object.keys(levelGroups).map(Number).sort((a, b) => b - a);

  levels.forEach((level, levelIndex) => {
    const nodesInLevel = levelGroups[level];
    const y = levelIndex * 150 + 50;
    const totalWidth = (nodesInLevel.length - 1) * 200;
    const startX = -totalWidth / 2;

    nodesInLevel.forEach((node, nodeIndex) => {
      layoutedNodes.push({
        ...node,
        position: {
          x: startX + nodeIndex * 200,
          y: y
        }
      });
    });
  });

  return layoutedNodes;
};

// 将B+树转换为React Flow数据：遍历算法中的所有节点，创建可视化节点和边，处理父子关系和兄弟指针
const convertBPlusTreeToFlowData = (algorithm: BPlusTreeAlgorithm, order: number): { nodes: Node<BPlusNodeData>[], edges: Edge[] } => {
  const allNodes = algorithm.getAllNodes();
  const reactFlowNodes: Node<BPlusNodeData>[] = [];
  const reactFlowEdges: Edge[] = [];

  if (allNodes.length === 0) {
    return { nodes: reactFlowNodes, edges: reactFlowEdges };
  }

  allNodes.forEach(node => {
    // 使用节点自身的level属性，如果没有则计算
    let level = node.level;
    if (level === undefined) {
      level = 0;
      let current = node;
      while (current.parent) {
        level++;
        current = current.parent;
      }
    }

    // 准备keys数组，确保长度为order-1
    const nodeKeys = node.keys.slice(0, node.numKeys);
    const paddedKeys = [...nodeKeys, ...Array(Math.max(0, order - 1 - nodeKeys.length)).fill(null)];

    // 准备pointers数组
    let paddedPointers: (string | null)[];
    if (node.isLeaf) {
      // 叶子节点不需要指针
      paddedPointers = Array(order).fill(null);
    } else {
      // 内部节点需要填充子节点的graphicID
      paddedPointers = Array(order).fill(null);
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child, index) => {
          if (child && child.graphicID && index < order) {
            paddedPointers[index] = child.graphicID;
          }
        });
      }
    }

    reactFlowNodes.push({
      id: node.graphicID,
      type: node.isLeaf ? 'bPlusLeafNode' : 'bPlusInternalNode',
      position: { x: 0, y: 0 },
      data: {
        keys: paddedKeys,
        pointers: paddedPointers,
        isLeaf: node.isLeaf,
        level: level,
        order: order,
        next: node.next?.graphicID || null
      }
    });

    // 创建父子关系的边
    if (!node.isLeaf && node.children && Array.isArray(node.children)) {
      node.children.forEach((child, index) => {
        if (child &&
            child.graphicID &&
            node.graphicID &&
            index <= node.numKeys &&
            typeof child.graphicID === 'string' &&
            typeof node.graphicID === 'string' &&
            child.graphicID.trim() !== '' &&
            node.graphicID.trim() !== '') {

          const edgeId = `${node.graphicID}-${child.graphicID}`;
          const sourceHandle = `pointer-${index}`;

          // 确保边ID唯一且有效
          if (!reactFlowEdges.some(edge => edge.id === edgeId)) {
            reactFlowEdges.push({
              id: edgeId,
              source: node.graphicID,
              target: child.graphicID,
              sourceHandle: sourceHandle,
              targetHandle: 'top',
              type: 'straight',
              animated: false,
              markerEnd: {
                type: MarkerType.ArrowClosed,
                width: 15,
                height: 15,
              },
            });
          }
        }
      });
    }

    // 叶子节点的兄弟指针
    if (node.isLeaf &&
        node.next &&
        node.next.graphicID &&
        node.graphicID &&
        typeof node.next.graphicID === 'string' &&
        typeof node.graphicID === 'string' &&
        node.next.graphicID.trim() !== '' &&
        node.graphicID.trim() !== '') {

      const edgeId = `${node.graphicID}-next-${node.next.graphicID}`;

      // 确保边ID唯一且有效
      if (!reactFlowEdges.some(edge => edge.id === edgeId)) {
        reactFlowEdges.push({
          id: edgeId,
          source: node.graphicID,
          target: node.next.graphicID,
          sourceHandle: 'sibling',
          targetHandle: 'sibling-target',
          type: 'straight',
          animated: false,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 15,
            height: 15,
          },
          style: { stroke: 'var(--secondary-text)' }
        });
      }
    }
  });

  return { nodes: reactFlowNodes, edges: reactFlowEdges };
};

// 自定义节点类型
const nodeTypes = {
  bPlusInternalNode: BPlusInternalNode,
  bPlusLeafNode: BPlusLeafNode,
};

interface BPlusTreeVisualizerCoreProps {
  initialKeys?: (number | string)[];
  order: number;
  
  // 外部状态控制
  externalNodes?: Node<BPlusNodeData>[];
  externalEdges?: Edge[];
  
  // 状态变更回调
  onStateChange?: (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => void;

  // 操作命令接口
  onInsert?: (key: number) => Promise<void>;
  onDelete?: (key: number) => Promise<void>;
  onReset?: () => Promise<void>;
}

const BPlusTreeVisualizerCore: React.FC<BPlusTreeVisualizerCoreProps> = ({
  initialKeys = [],
  order,
  externalNodes,
  externalEdges,
  onStateChange,
  onInsert,
  onDelete,
  onReset
}) => {
  // React Flow 状态
  const [nodes, setNodes, onNodesChange] = useNodesState<Node<BPlusNodeData>>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  
  // 消息提示状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // 核心实例引用
  const bPlusTreeAlgorithmRef = useRef<BPlusTreeAlgorithm | null>(null);
  const animationManagerRef = useRef<AnimationManager | null>(null);
  const commandExecutorRef = useRef<CommandExecutor | null>(null);

  // 显示消息的辅助函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // 从外部节点重建算法状态的辅助函数
  const rebuildAlgorithmFromNodes = useCallback((nodes: Node<BPlusNodeData>[]) => {
    if (!bPlusTreeAlgorithmRef.current) return;

    // 清空当前算法状态
    bPlusTreeAlgorithmRef.current.clear();

    // 从节点数据中提取所有键值
    const keys: number[] = [];
    nodes.forEach(node => {
      if (node.data.keys) {
        node.data.keys.forEach(key => {
          if (typeof key === 'number') {
            keys.push(key);
          }
        });
      }
    });

    // 去重并排序
    const uniqueKeys = [...new Set(keys)].sort((a, b) => a - b);

    // 重新插入所有键值来重建树结构
    uniqueKeys.forEach(key => {
      try {
        bPlusTreeAlgorithmRef.current!.insertElement(key);
      } catch (error) {
        console.warn(`Failed to rebuild key ${key}:`, error);
      }
    });
  }, []);

  // 更新视图函数
  const updateView = useCallback((operation?: 'insert' | 'delete' | 'reset', operationKey?: number) => {
    if (!bPlusTreeAlgorithmRef.current) return;

    try {
      const { nodes: newNodes, edges: newEdges } = convertBPlusTreeToFlowData(
        bPlusTreeAlgorithmRef.current,
        order
      );

      // 验证节点和边数据的有效性
      const validNodes = newNodes.filter(node =>
        node.id &&
        typeof node.id === 'string' &&
        node.id.trim() !== ''
      );

      const validEdges = newEdges.filter(edge =>
        edge.id &&
        edge.source &&
        edge.target &&
        typeof edge.id === 'string' &&
        typeof edge.source === 'string' &&
        typeof edge.target === 'string' &&
        edge.id.trim() !== '' &&
        edge.source.trim() !== '' &&
        edge.target.trim() !== ''
      );

      const layoutedNewNodes = layoutNodes(validNodes, validEdges);
      setNodes(layoutedNewNodes);
      setEdges(validEdges);

      // 触发状态变更通知
      if (onStateChange) {
        const keys = bPlusTreeAlgorithmRef.current.getAllKeys();
        onStateChange({
          nodes: layoutedNewNodes,
          edges: validEdges,
          keys,
          operation,
          operationKey
        });
      }
    } catch (error) {
      console.error('Error updating view:', error);
      showMessage('视图更新失败', 'error');
    }
  }, [order, onStateChange, showMessage]);

  // 初始化核心实例
  useEffect(() => {
    bPlusTreeAlgorithmRef.current = new BPlusTreeAlgorithm(order);
    animationManagerRef.current = new AnimationManager();
    commandExecutorRef.current = new CommandExecutor({
      setNodes,
      setEdges,
      showMessage
    });

    // 如果有外部状态，优先使用外部状态
    if (externalNodes && externalEdges) {
      setNodes(externalNodes);
      setEdges(externalEdges);
      rebuildAlgorithmFromNodes(externalNodes);
    } else if (initialKeys.length > 0) {
      // 使用初始键值
      initialKeys.forEach(key => {
        if (typeof key === 'number') {
          try {
            bPlusTreeAlgorithmRef.current!.insertElement(key);
          } catch (error) {
            console.warn(`Failed to insert initial key ${key}:`, error);
          }
        }
      });
      updateView();
    } else {
      // 显示空树
      updateView();
    }
  }, [order, externalNodes, externalEdges, initialKeys, rebuildAlgorithmFromNodes, updateView]);

  // 监听外部状态变化并同步到内部状态
  useEffect(() => {
    if (externalNodes && externalEdges) {
      setNodes(externalNodes);
      setEdges(externalEdges);
      rebuildAlgorithmFromNodes(externalNodes);
    }
  }, [externalNodes, externalEdges, rebuildAlgorithmFromNodes]);

  // 暴露操作接口给外部组件
  useEffect(() => {
    if (onInsert) {
      // 这里可以添加插入操作的实现
    }
    if (onDelete) {
      // 这里可以添加删除操作的实现
    }
    if (onReset) {
      // 这里可以添加重置操作的实现
    }
  }, [onInsert, onDelete, onReset]);

  // 关闭消息提示
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box sx={{ 
      width: '100%', 
      height: '100%', 
      position: 'relative',
      backgroundColor: 'var(--background)'
    }}>
      <ReactFlowProvider>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{
            padding: 0.2,
            includeHiddenNodes: false,
          }}
          defaultEdgeOptions={{
            type: 'smoothstep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 20,
              height: 20,
              color: '#666',
            },
            style: {
              strokeWidth: 2,
              stroke: '#666',
            },
          }}
          proOptions={{ hideAttribution: true }}
        >
          <Controls />
          <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
          
          {/* 状态面板 */}
          <Panel position="top-left">
            <Box sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 0, 0, 0.1)',
              borderRadius: 1,
              p: 1,
              fontSize: '0.875rem'
            }}>
              阶数: {order} | 节点数: {nodes.length} | 边数: {edges.length}
            </Box>
          </Panel>
        </ReactFlow>
      </ReactFlowProvider>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BPlusTreeVisualizerCore;
