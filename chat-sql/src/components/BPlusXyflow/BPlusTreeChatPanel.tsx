/**
 * B+树对话框面板组件
 * 预留AI对话功能区域，参考LLMWindow模块样式
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  IconButton,
  Divider
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as AIIcon,
  Help as HelpIcon,
  Psychology as PsychologyIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';

const BPlusTreeChatPanel: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Array<{
    id: string;
    type: 'user' | 'ai';
    content: string;
    timestamp: number;
  }>>([]);

  // 处理发送消息
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage = {
      id: `msg-${Date.now()}`,
      type: 'user' as const,
      content: inputValue.trim(),
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = {
        id: `ai-${Date.now()}`,
        type: 'ai' as const,
        content: '这是一个预留的AI对话功能。未来将支持B+树相关的智能问答和操作建议。',
        timestamp: Date.now()
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  // 处理快捷问题
  const handleQuickQuestion = (question: string) => {
    setInputValue(question);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 头部 */}
      <Box sx={{ 
        p: 2, 
        borderBottom: '1px solid', 
        borderColor: 'divider',
        backgroundColor: 'var(--background)',
        flexShrink: 0
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AIIcon sx={{ color: 'var(--primary-main)' }} />
          <Typography variant="h6" sx={{ 
            color: 'var(--primary-text)', 
            fontWeight: 'bold'
          }}>
            AI助手
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ 
          color: 'var(--secondary-text)',
          mt: 0.5
        }}>
          B+树智能问答与操作建议
        </Typography>
      </Box>

      {/* 消息区域 */}
      <Box sx={{ 
        flex: 1, 
        overflow: 'auto',
        p: 2,
        display: 'flex',
        flexDirection: 'column'
      }}>
        {messages.length === 0 ? (
          // 空状态 - 参考LLMWindow的居中布局
          <Box sx={{ 
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center',
            gap: 3
          }}>
            <Box sx={{ 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 80,
              height: 80,
              borderRadius: '50%',
              backgroundColor: 'var(--primary-light)',
              mb: 2
            }}>
              <PsychologyIcon sx={{ 
                fontSize: 40, 
                color: 'var(--primary-main)' 
              }} />
            </Box>

            <Typography variant="h6" sx={{ 
              color: 'var(--primary-text)',
              fontWeight: 'bold'
            }}>
              B+树智能助手
            </Typography>

            <Typography variant="body2" sx={{ 
              color: 'var(--secondary-text)',
              maxWidth: '80%',
              lineHeight: 1.6
            }}>
              我可以帮助您理解B+树的结构和操作，提供插入、删除策略建议，
              解答相关问题。请在下方输入您的问题。
            </Typography>

            {/* 快捷问题按钮 */}
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              gap: 1,
              width: '100%',
              maxWidth: 300
            }}>
              <Button
                variant="outlined"
                startIcon={<HelpIcon />}
                onClick={() => handleQuickQuestion('B+树的基本特性是什么？')}
                sx={{ justifyContent: 'flex-start' }}
              >
                B+树基本特性
              </Button>
              <Button
                variant="outlined"
                startIcon={<AutoAwesomeIcon />}
                onClick={() => handleQuickQuestion('如何优化B+树的插入操作？')}
                sx={{ justifyContent: 'flex-start' }}
              >
                插入操作优化
              </Button>
              <Button
                variant="outlined"
                startIcon={<PsychologyIcon />}
                onClick={() => handleQuickQuestion('分析当前B+树的结构特点')}
                sx={{ justifyContent: 'flex-start' }}
              >
                结构分析建议
              </Button>
            </Box>
          </Box>
        ) : (
          // 消息列表
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {messages.map((message) => (
              <Paper
                key={message.id}
                elevation={1}
                sx={{
                  p: 2,
                  backgroundColor: message.type === 'user' 
                    ? 'var(--primary-light)' 
                    : 'var(--background)',
                  alignSelf: message.type === 'user' ? 'flex-end' : 'flex-start',
                  maxWidth: '85%',
                  borderRadius: 2,
                  border: '1px solid var(--card-border)'
                }}
              >
                <Typography variant="body2" sx={{ 
                  color: 'var(--primary-text)',
                  lineHeight: 1.5
                }}>
                  {message.content}
                </Typography>
                <Typography variant="caption" sx={{ 
                  color: 'var(--secondary-text)',
                  mt: 1,
                  display: 'block'
                }}>
                  {new Date(message.timestamp).toLocaleTimeString()}
                </Typography>
              </Paper>
            ))}
          </Box>
        )}
      </Box>

      {/* 输入区域 */}
      <Box sx={{ 
        p: 2, 
        borderTop: '1px solid', 
        borderColor: 'divider',
        backgroundColor: 'var(--background)',
        flexShrink: 0
      }}>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            multiline
            maxRows={3}
            placeholder="询问B+树相关问题..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            sx={{ 
              flex: 1,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'var(--input-bg)',
                '& fieldset': {
                  borderColor: 'var(--input-border)',
                },
                '&:hover fieldset': {
                  borderColor: 'var(--primary-main)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'var(--primary-main)',
                },
              },
              '& .MuiInputBase-input': {
                color: 'var(--input-text)',
              },
              '& .MuiInputBase-input::placeholder': {
                color: 'var(--secondary-text)',
                opacity: 1,
              }
            }}
          />
          <IconButton
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            sx={{
              backgroundColor: 'var(--primary-main)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'var(--primary-dark)',
              },
              '&:disabled': {
                backgroundColor: 'var(--action-disabled)',
                color: 'var(--action-disabled-text)',
              }
            }}
          >
            <SendIcon />
          </IconButton>
        </Box>

        <Typography variant="caption" sx={{ 
          color: 'var(--secondary-text)',
          mt: 1,
          display: 'block',
          textAlign: 'center'
        }}>
          按 Enter 发送，Shift + Enter 换行
        </Typography>
      </Box>
    </Box>
  );
};

export default BPlusTreeChatPanel;
