/**
 * B+树操作面板组件
 * 包含插入、删除、重置等操作控件，以及动画控制和执行模式选择
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Divider,
  ToggleButtonGroup,
  ToggleButton,
  FormControlLabel,
  Switch,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { HistorySession } from '@/types/bPlusHistory';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

// 执行模式类型定义
type ExecutionMode = 'instant' | 'step-by-step' | 'breakpoint';

interface BPlusTreeOperationPanelProps {
  selectedSession: HistorySession | null;
  onStateChange: (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => void;

  // 操作命令接口
  onInsert?: (key: number) => Promise<void>;
  onDelete?: (key: number) => Promise<void>;
  onReset?: () => Promise<void>;
}

const BPlusTreeOperationPanel: React.FC<BPlusTreeOperationPanelProps> = ({
  selectedSession,
  onStateChange,
  onInsert,
  onDelete,
  onReset
}) => {
  // 状态管理
  const [insertValue, setInsertValue] = useState('');
  const [deleteValue, setDeleteValue] = useState('');
  const [executionMode, setExecutionMode] = useState<ExecutionMode>('breakpoint');
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true);
  const [error, setError] = useState('');

  // 验证输入
  const validateInput = (value: string): boolean => {
    const trimmedValue = value.trim();
    if (trimmedValue === '') return false;
    const num = parseInt(trimmedValue);
    return !isNaN(num) && isFinite(num);
  };

  // 处理插入操作
  const handleInsert = async () => {
    if (!validateInput(insertValue)) {
      setError('请输入有效的整数（支持正负数和0）');
      return;
    }

    const key = parseInt(insertValue);
    setError('');

    try {
      if (onInsert) {
        await onInsert(key);
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        onStateChange({
          nodes: [],
          edges: [],
          keys: [key],
          operation: 'insert',
          operationKey: key
        });
      }
      setInsertValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '插入操作失败');
    }
  };

  // 处理删除操作
  const handleDelete = async () => {
    if (!validateInput(deleteValue)) {
      setError('请输入有效的整数（支持正负数和0）');
      return;
    }

    const key = parseInt(deleteValue);
    setError('');

    try {
      if (onDelete) {
        await onDelete(key);
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        onStateChange({
          nodes: [],
          edges: [],
          keys: [],
          operation: 'delete',
          operationKey: key
        });
      }
      setDeleteValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '删除操作失败');
    }
  };

  // 处理重置操作
  const handleReset = async () => {
    setError('');

    try {
      if (onReset) {
        await onReset();
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        onStateChange({
          nodes: [],
          edges: [],
          keys: [],
          operation: 'reset'
        });
      }
      setInsertValue('');
      setDeleteValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '重置操作失败');
    }
  };

  // 处理执行模式变更
  const handleExecutionModeChange = (_: React.MouseEvent<HTMLElement>, newMode: ExecutionMode) => {
    if (newMode !== null) {
      setExecutionMode(newMode);
    }
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      p: 2,
      overflow: 'auto'
    }}>
      {/* 标题 */}
      <Typography variant="h6" sx={{ 
        color: 'var(--primary-text)', 
        fontWeight: 'bold',
        mb: 2
      }}>
        B+树操作控制
      </Typography>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* 会话信息 */}
      {selectedSession && (
        <Paper elevation={1} sx={{ p: 2, mb: 2, backgroundColor: 'var(--background)' }}>
          <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
            当前会话: {selectedSession.name}
          </Typography>
          <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
            B+树阶数: {selectedSession.order}
          </Typography>
        </Paper>
      )}

      {/* 基本操作区域 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ 
          color: 'var(--primary-text)', 
          mb: 2,
          fontWeight: 'bold'
        }}>
          基本操作
        </Typography>

        {/* 插入操作 */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            size="small"
            label="插入键值"
            value={insertValue}
            onChange={(e) => setInsertValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleInsert()}
            sx={{ flex: 1 }}
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleInsert}
            disabled={!selectedSession}
            sx={{ backgroundColor: 'var(--success-main)' }}
          >
            插入
          </Button>
        </Box>

        {/* 删除操作 */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            size="small"
            label="删除键值"
            value={deleteValue}
            onChange={(e) => setDeleteValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleDelete()}
            sx={{ flex: 1 }}
          />
          <Button
            variant="contained"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
            disabled={!selectedSession}
            sx={{ backgroundColor: 'var(--error-main)' }}
          >
            删除
          </Button>
        </Box>

        {/* 重置操作 */}
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={!selectedSession}
          fullWidth
          sx={{ color: 'var(--warning-main)', borderColor: 'var(--warning-main)' }}
        >
          重置B+树
        </Button>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* 动画设置区域 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ 
          color: 'var(--primary-text)', 
          mb: 2,
          fontWeight: 'bold'
        }}>
          动画设置
        </Typography>

        {/* 动画开关 */}
        <FormControlLabel
          control={
            <Switch
              checked={isAnimationEnabled}
              onChange={(e) => setIsAnimationEnabled(e.target.checked)}
            />
          }
          label="启用动画"
          sx={{ mb: 2 }}
        />

        {/* 执行模式选择 */}
        {/* {isAnimationEnabled && (
          <Box>
            <Typography variant="body2" sx={{ mb: 1, color: 'var(--secondary-text)' }}>
              执行模式
            </Typography>
            <ToggleButtonGroup
              value={executionMode}
              exclusive
              onChange={handleExecutionModeChange}
              size="small"
              fullWidth
            >
              <ToggleButton value="instant">
                立即执行
              </ToggleButton>
              <ToggleButton value="step-by-step">
                逐步执行
              </ToggleButton>
              <ToggleButton value="breakpoint">
                断点执行
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        )} */}
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* 快捷操作区域 */}
      <Box sx={{ mt: 'auto' }}>
        <Typography variant="subtitle2" sx={{ 
          color: 'var(--primary-text)', 
          mb: 2,
          fontWeight: 'bold'
        }}>
          快捷操作
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<PlayIcon />}
            disabled={!selectedSession}
            size="small"
          >
            批量插入
          </Button>
          <Button
            variant="outlined"
            startIcon={<SpeedIcon />}
            disabled={!selectedSession}
            size="small"
          >
            性能测试
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default BPlusTreeOperationPanel;
