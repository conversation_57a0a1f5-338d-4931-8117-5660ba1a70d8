/**
 * B+树操作面板组件
 * 包含插入、删除、重置等操作控件，以及动画控制和执行模式选择
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Divider,
  FormControlLabel,
  Switch,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { HistorySession } from '@/types/bPlusHistory';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';



interface BPlusTreeOperationPanelProps {
  selectedSession: HistorySession | null;
  onStateChange: (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => void;

  // 操作命令接口
  onInsert?: (key: number) => Promise<void>;
  onDelete?: (key: number) => Promise<void>;
  onReset?: () => Promise<void>;
}

const BPlusTreeOperationPanel: React.FC<BPlusTreeOperationPanelProps> = ({
  selectedSession,
  onStateChange,
  onInsert,
  onDelete,
  onReset
}) => {
  // 状态管理
  const [insertValue, setInsertValue] = useState('');
  const [deleteValue, setDeleteValue] = useState('');
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true);
  const [error, setError] = useState('');

  // 验证输入：支持单个数值或空格分隔的多个数值
  const validateInput = (value: string): boolean => {
    const trimmedValue = value.trim();
    if (trimmedValue === '') return false;

    // 支持空格分隔的多个数值
    const values = trimmedValue.split(/\s+/);
    return values.every(val => {
      const num = parseInt(val);
      return !isNaN(num) && isFinite(num);
    });
  };

  // 解析输入为数值数组
  const parseInputValues = (value: string): number[] => {
    const trimmedValue = value.trim();
    if (trimmedValue === '') return [];

    return trimmedValue.split(/\s+/).map(val => parseInt(val)).filter(num => !isNaN(num) && isFinite(num));
  };

  // 处理插入操作：支持批量插入多个键值
  const handleInsert = async () => {
    if (!validateInput(insertValue)) {
      setError('请输入有效的整数（支持正负数和0），多个数值用空格分隔');
      return;
    }

    const keys = parseInputValues(insertValue);
    setError('');

    try {
      if (onInsert) {
        // 批量插入
        for (const key of keys) {
          await onInsert(key);
        }
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        for (const key of keys) {
          onStateChange({
            nodes: [],
            edges: [],
            keys: [key],
            operation: 'insert',
            operationKey: key
          });
        }
      }
      setInsertValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '插入操作失败');
    }
  };

  // 处理删除操作：支持批量删除多个键值
  const handleDelete = async () => {
    if (!validateInput(deleteValue)) {
      setError('请输入有效的整数（支持正负数和0），多个数值用空格分隔');
      return;
    }

    const keys = parseInputValues(deleteValue);
    setError('');

    try {
      if (onDelete) {
        // 批量删除
        for (const key of keys) {
          await onDelete(key);
        }
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        for (const key of keys) {
          onStateChange({
            nodes: [],
            edges: [],
            keys: [],
            operation: 'delete',
            operationKey: key
          });
        }
      }
      setDeleteValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '删除操作失败');
    }
  };

  // 处理重置操作
  const handleReset = async () => {
    setError('');

    try {
      if (onReset) {
        await onReset();
      } else {
        // 如果没有提供操作接口，直接触发状态变更
        onStateChange({
          nodes: [],
          edges: [],
          keys: [],
          operation: 'reset'
        });
      }
      setInsertValue('');
      setDeleteValue('');
    } catch (error) {
      setError(error instanceof Error ? error.message : '重置操作失败');
    }
  };



  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      p: 2,
      overflow: 'auto'
    }}>
      {/* 标题 */}
      <Typography variant="h6" sx={{ 
        color: 'var(--primary-text)', 
        fontWeight: 'bold',
        mb: 2
      }}>
        B+树操作控制
      </Typography>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* 会话信息 */}
      {selectedSession && (
        <Paper elevation={1} sx={{ p: 2, mb: 2, backgroundColor: 'var(--background)' }}>
          <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
            当前会话: {selectedSession.name}
          </Typography>
          <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
            B+树阶数: {selectedSession.order}
          </Typography>
        </Paper>
      )}

      {/* 基本操作区域 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ 
          color: 'var(--primary-text)', 
          mb: 2,
          fontWeight: 'bold'
        }}>
          基本操作
        </Typography>

        {/* 插入操作 */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            size="small"
            label="插入键值"
            placeholder="输入数值，多个用空格分隔，如：1 2 3"
            value={insertValue}
            onChange={(e) => setInsertValue(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleInsert()}
            sx={{ flex: 1 }}
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleInsert}
            disabled={!selectedSession}
            sx={{ backgroundColor: 'var(--success-main)' }}
          >
            插入
          </Button>
        </Box>

        {/* 删除操作 */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            size="small"
            label="删除键值"
            placeholder="输入数值，多个用空格分隔，如：1 2 3"
            value={deleteValue}
            onChange={(e) => setDeleteValue(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleDelete()}
            sx={{ flex: 1 }}
          />
          <Button
            variant="contained"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
            disabled={!selectedSession}
            sx={{ backgroundColor: 'var(--error-main)' }}
          >
            删除
          </Button>
        </Box>

        {/* 重置操作 */}
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={!selectedSession}
          fullWidth
          sx={{ color: 'var(--warning-main)', borderColor: 'var(--warning-main)' }}
        >
          重置B+树
        </Button>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* 动画设置区域 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ 
          color: 'var(--primary-text)', 
          mb: 2,
          fontWeight: 'bold'
        }}>
          动画设置
        </Typography>

        {/* 动画开关 */}
        <FormControlLabel
          control={
            <Switch
              checked={isAnimationEnabled}
              onChange={(e) => setIsAnimationEnabled(e.target.checked)}
            />
          }
          label="启用动画"
          sx={{ mb: 2 }}
        />

      </Box>
    </Box>
  );
};

export default BPlusTreeOperationPanel;
