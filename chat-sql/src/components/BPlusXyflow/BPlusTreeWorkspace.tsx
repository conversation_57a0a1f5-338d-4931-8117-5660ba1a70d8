/**
 * B+树工作区组件
 * 实现三区域布局：上方可视化、左下操作面板、右下对话框区域
 */

import React, { useRef, useCallback } from 'react';
import { Box } from '@mui/material';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import BPlusTreeVisualizerCore, { BPlusTreeVisualizerCoreRef } from './BPlusTreeVisualizerCore';
import BPlusTreeOperationPanel from './BPlusTreeOperationPanel';
import BPlusTreeChatPanel from './BPlusTreeChatPanel';
import { HistorySession } from '@/types/bPlusHistory';
import { Node, Edge } from '@xyflow/react';
import { BPlusNodeData } from '@/components/utils/bPlusTreeToReactFlow';

interface BPlusTreeWorkspaceProps {
  selectedSession: HistorySession | null;
  currentNodes: Node<BPlusNodeData>[];
  currentEdges: Edge[];
  onStateChange: (state: {
    nodes: Node<BPlusNodeData>[];
    edges: Edge[];
    keys: number[];
    operation?: 'insert' | 'delete' | 'reset';
    operationKey?: number;
  }) => void;

  // 操作回调
  onInsert?: (key: number) => Promise<void>;
  onDelete?: (key: number) => Promise<void>;
  onReset?: () => Promise<void>;
}

const BPlusTreeWorkspace: React.FC<BPlusTreeWorkspaceProps> = ({
  selectedSession,
  currentNodes,
  currentEdges,
  onStateChange,
  onInsert,
  onDelete,
  onReset
}) => {
  // 可视化组件的ref
  const visualizerRef = useRef<BPlusTreeVisualizerCoreRef>(null);

  // 包装操作回调，通过ref调用实际操作
  const handleInsert = useCallback(async (key: number) => {
    if (visualizerRef.current) {
      await visualizerRef.current.insertKey(key);
    } else if (onInsert) {
      await onInsert(key);
    }
  }, [onInsert]);

  const handleDelete = useCallback(async (key: number) => {
    if (visualizerRef.current) {
      await visualizerRef.current.deleteKey(key);
    } else if (onDelete) {
      await onDelete(key);
    }
  }, [onDelete]);

  const handleReset = useCallback(async () => {
    if (visualizerRef.current) {
      await visualizerRef.current.resetTree();
    } else if (onReset) {
      await onReset();
    }
  }, [onReset]);
  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <PanelGroup direction="vertical">
        {/* 上方区域：B+树可视化 */}
        <Panel 
          defaultSize={60} 
          minSize={40}
          style={{
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'var(--background)',
            border: '1px solid var(--card-border)',
            borderRadius: '8px',
            margin: '8px',
            overflow: 'hidden'
          }}
        >
          <BPlusTreeVisualizerCore
            ref={visualizerRef}
            initialKeys={[]}
            order={selectedSession?.order || 3}
            externalNodes={currentNodes}
            externalEdges={currentEdges}
            onStateChange={onStateChange}
          />
        </Panel>

        {/* 拖拽手柄 */}
        <PanelResizeHandle 
          style={{
            height: '6px',
            backgroundColor: 'transparent',
            cursor: 'row-resize',
            margin: '0 8px',
            position: 'relative'
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: '60px',
              height: '3px',
              backgroundColor: 'var(--secondary-text)',
              borderRadius: '2px',
              opacity: 0.3,
              '&:hover': {
                opacity: 0.6
              }
            }}
          />
        </PanelResizeHandle>

        {/* 下方区域：左右分栏 */}
        <Panel 
          defaultSize={40} 
          minSize={25}
          style={{
            display: 'flex',
            flexDirection: 'column',
            margin: '0 8px 8px 8px',
            overflow: 'hidden'
          }}
        >
          <PanelGroup direction="horizontal">
            {/* 左下：操作面板 */}
            <Panel 
              defaultSize={50} 
              minSize={30}
              style={{
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: 'var(--card-bg)',
                border: '1px solid var(--card-border)',
                borderRadius: '8px',
                marginRight: '4px',
                overflow: 'hidden'
              }}
            >
              <BPlusTreeOperationPanel
                selectedSession={selectedSession}
                onStateChange={onStateChange}
                onInsert={handleInsert}
                onDelete={handleDelete}
                onReset={handleReset}
              />
            </Panel>

            {/* 拖拽手柄 */}
            <PanelResizeHandle 
              style={{
                width: '6px',
                backgroundColor: 'transparent',
                cursor: 'col-resize',
                margin: '0 2px',
                position: 'relative'
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '3px',
                  height: '40px',
                  backgroundColor: 'var(--secondary-text)',
                  borderRadius: '2px',
                  opacity: 0.3,
                  '&:hover': {
                    opacity: 0.6
                  }
                }}
              />
            </PanelResizeHandle>

            {/* 右下：对话框区域 */}
            <Panel 
              defaultSize={50} 
              minSize={30}
              style={{
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: 'var(--card-bg)',
                border: '1px solid var(--card-border)',
                borderRadius: '8px',
                marginLeft: '4px',
                overflow: 'hidden'
              }}
            >
              <BPlusTreeChatPanel />
            </Panel>
          </PanelGroup>
        </Panel>
      </PanelGroup>
    </Box>
  );
};

export default BPlusTreeWorkspace;
