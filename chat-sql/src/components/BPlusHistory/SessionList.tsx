/**
 * 会话列表组件
 * 显示所有历史会话，支持选择、重命名、删除等操作
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Menu,
  MenuItem,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { HistorySession } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';

interface SessionListProps {
  sessions: HistorySession[];
  onSessionSelect: (session: HistorySession) => void;
  onSessionsChange: () => void;
  showMessage: (message: string, severity?: 'success' | 'info' | 'warning' | 'error') => void;
}

const SessionList: React.FC<SessionListProps> = ({
  sessions,
  onSessionSelect,
  onSessionsChange,
  showMessage
}) => {
  // 状态管理
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSession, setSelectedSession] = useState<HistorySession | null>(null);
  const [newSessionName, setNewSessionName] = useState('');
  const [newSessionOrder, setNewSessionOrder] = useState(3);
  const [renameValue, setRenameValue] = useState('');
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  // 格式化相对时间
  const formatRelativeTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 30) return `${days}天前`;
    return formatTime(timestamp);
  };

  // 处理创建新会话
  const handleCreateSession = async () => {
    if (!newSessionName.trim()) {
      showMessage('请输入会话名称', 'warning');
      return;
    }

    try {
      const storage = await getBPlusHistoryStorage();
      
      // 创建初始步骤
      const initialStep = {
        id: `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        operation: 'initial' as const,
        timestamp: Date.now(),
        nodes: [],
        edges: [],
        keys: [],
        description: '初始化空树'
      };

      const result = await storage.createSession(newSessionName.trim(), newSessionOrder, initialStep);

      if (result.success) {
        showMessage('会话创建成功', 'success');
        setCreateDialogOpen(false);
        setNewSessionName('');
        setNewSessionOrder(3);
        onSessionsChange();
      } else {
        showMessage(result.error || '创建会话失败', 'error');
      }
    } catch (error) {
      showMessage('创建会话时发生错误', 'error');
    }
  };

  // 处理重命名会话
  const handleRenameSession = async () => {
    if (!selectedSession || !renameValue.trim()) {
      showMessage('请输入新的会话名称', 'warning');
      return;
    }

    try {
      const storage = await getBPlusHistoryStorage();
      const result = await storage.renameSession(selectedSession.id, renameValue.trim());

      if (result.success) {
        showMessage('会话重命名成功', 'success');
        setRenameDialogOpen(false);
        setRenameValue('');
        setSelectedSession(null);
        onSessionsChange();
      } else {
        showMessage(result.error || '重命名会话失败', 'error');
      }
    } catch (error) {
      showMessage('重命名会话时发生错误', 'error');
    }
  };

  // 处理删除会话
  const handleDeleteSession = async () => {
    if (!selectedSession) return;

    try {
      const storage = await getBPlusHistoryStorage();
      const result = await storage.deleteSession(selectedSession.id);

      if (result.success) {
        showMessage('会话删除成功', 'success');
        setDeleteDialogOpen(false);
        setSelectedSession(null);
        onSessionsChange();
      } else {
        showMessage(result.error || '删除会话失败', 'error');
      }
    } catch (error) {
      showMessage('删除会话时发生错误', 'error');
    }
  };

  // 打开菜单
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, session: HistorySession) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setSelectedSession(session);
  };

  // 关闭菜单
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // 打开重命名对话框
  const handleRenameDialogOpen = () => {
    if (selectedSession) {
      setRenameValue(selectedSession.name);
      setRenameDialogOpen(true);
    }
    handleMenuClose();
  };

  // 打开删除对话框
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  return (
    <Box>
      {/* 头部操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        {/* <Typography variant="h6" sx={{ color: 'var(--primary-text)' }}>
          历史会话 ({sessions.length})
        </Typography> */}
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          sx={{ backgroundColor: 'var(--primary-main)' }}
        >
          新建会话
        </Button>
      </Box>

      {/* 会话列表 */}
      {sessions.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <HistoryIcon sx={{ fontSize: 64, color: 'var(--secondary-text)', mb: 2 }} />
          <Typography variant="h6" sx={{ color: 'var(--secondary-text)', mb: 1 }}>
            暂无历史会话
          </Typography>
          <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
            创建新会话开始记录B+树操作历史
          </Typography>
        </Box>
      ) : (
        <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>
          {sessions.map((session) => (
            <Card
              key={session.id}
              sx={{
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 4
                }
              }}
              onClick={() => onSessionSelect(session)}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" component="h3" sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}>
                    {session.name}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, session)}
                    sx={{ color: 'var(--secondary-text)' }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </Box>

                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label={`阶数: ${session.order}`}
                    size="small"
                    variant="outlined"
                    sx={{ color: 'var(--primary-main)', borderColor: 'var(--primary-main)' }}
                  />
                  <Chip
                    label={`${session.steps.length} 步骤`}
                    size="small"
                    variant="outlined"
                    sx={{ color: 'var(--info-main)', borderColor: 'var(--info-main)' }}
                  />
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <ScheduleIcon sx={{ fontSize: 16, color: 'var(--secondary-text)' }} />
                  <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
                    创建于 {formatTime(session.createdAt)}
                  </Typography>
                </Box>

                <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
                  最后更新: {formatRelativeTime(session.updatedAt)}
                </Typography>
              </CardContent>
            </Card>
          ))}
        </Box>
      )}

      {/* 右键菜单 */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleRenameDialogOpen}>
          <EditIcon sx={{ mr: 1, fontSize: 20 }} />
          重命名
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeleteDialogOpen} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1, fontSize: 20 }} />
          删除
        </MenuItem>
      </Menu>

      {/* 创建会话对话框 */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>创建新会话</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="会话名称"
            fullWidth
            variant="outlined"
            value={newSessionName}
            onChange={(e) => setNewSessionName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="B+树阶数"
            type="number"
            fullWidth
            variant="outlined"
            value={newSessionOrder}
            onChange={(e) => setNewSessionOrder(parseInt(e.target.value) || 3)}
            slotProps={{
              htmlInput: { min: 3, max: 10 }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
          <Button onClick={handleCreateSession} variant="contained">创建</Button>
        </DialogActions>
      </Dialog>

      {/* 重命名对话框 */}
      <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>重命名会话</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="新名称"
            fullWidth
            variant="outlined"
            value={renameValue}
            onChange={(e) => setRenameValue(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRenameDialogOpen(false)}>取消</Button>
          <Button onClick={handleRenameSession} variant="contained">确认</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除会话 "{selectedSession?.name}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button onClick={handleDeleteSession} variant="contained" color="error">删除</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SessionList;
