/**
 * B+树历史管理面板组件
 * 可嵌入的历史管理面板，包含会话列表和步骤时间线两个Tab
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
  Paper
} from '@mui/material';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';
import SessionList from './SessionList';
import StepTimeline from './StepTimeline';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab面板组件
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`history-tabpanel-${index}`}
      aria-labelledby={`history-tab-${index}`}
      {...other}
      style={{ height: value === index ? '100%' : 0, overflow: 'hidden' }}
    >
      {value === index && (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface BPlusHistoryPanelProps {
  selectedSession: HistorySession | null;
  selectedStep: HistoryStep | null;
  onSessionSelect: (session: HistorySession) => void;
  onStepSelect: (step: HistoryStep, stepIndex: number) => void;
}

const BPlusHistoryPanel: React.FC<BPlusHistoryPanelProps> = ({
  selectedSession,
  selectedStep,
  onSessionSelect,
  onStepSelect
}) => {
  // 状态管理
  const [tabValue, setTabValue] = useState(0);
  const [sessions, setSessions] = useState<HistorySession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  
  // 消息提示状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // 显示消息的辅助函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // 加载所有会话
  const loadSessions = useCallback(async () => {
    try {
      setLoading(true);
      const storage = await getBPlusHistoryStorage();
      const result = await storage.getAllSessions({
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      });

      if (result.success) {
        setSessions(result.data || []);
      } else {
        setError(result.error || '加载会话失败');
        showMessage(result.error || '加载会话失败', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载会话时发生未知错误';
      setError(errorMessage);
      showMessage(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  }, [showMessage]);

  // 初始化加载
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  // 处理会话选择
  const handleSessionSelect = useCallback((session: HistorySession) => {
    onSessionSelect(session);
    // 切换到步骤时间线标签页
    setTabValue(1);
  }, [onSessionSelect]);

  // 处理步骤选择
  const handleStepSelect = useCallback(async (step: HistoryStep, stepIndex: number) => {
    if (!selectedSession) return;

    try {
      const storage = await getBPlusHistoryStorage();
      const result = await storage.updateCurrentStep(selectedSession.id, stepIndex);

      if (result.success) {
        onStepSelect(step, stepIndex);
        // 更新会话列表中的对应会话
        setSessions(prev => prev.map(s => 
          s.id === selectedSession.id ? result.data : s
        ));
        showMessage(`已回溯到步骤: ${step.description}`, 'success');
      } else {
        showMessage(result.error || '回溯失败', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '回溯时发生未知错误';
      showMessage(errorMessage, 'error');
    }
  }, [selectedSession, onStepSelect, showMessage]);

  // 处理Tab切换
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 关闭消息提示
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading) {
    return (
      <Box sx={{ 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center' 
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ m: 1, flexShrink: 0 }}>
          {error}
        </Alert>
      )}

      {/* Tab导航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', flexShrink: 0 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange} 
          aria-label="历史管理标签页"
          variant="fullWidth"
        >
          <Tab 
            label="历史会话" 
            id="history-tab-0" 
            aria-controls="history-tabpanel-0" 
          />
          <Tab 
            label="操作步骤" 
            id="history-tab-1" 
            aria-controls="history-tabpanel-1"
            disabled={!selectedSession}
          />
        </Tabs>
      </Box>

      {/* Tab内容区域 */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <TabPanel value={tabValue} index={0}>
          <SessionList
            sessions={sessions}
            onSessionSelect={handleSessionSelect}
            onSessionsChange={loadSessions}
            showMessage={showMessage}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {selectedSession ? (
            <StepTimeline
              session={selectedSession}
              selectedStep={selectedStep}
              onStepSelect={handleStepSelect}
            />
          ) : (
            <Box sx={{ 
              height: '100%', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center' 
            }}>
              <Typography variant="body1" sx={{ color: 'var(--secondary-text)' }}>
                请先选择一个会话
              </Typography>
            </Box>
          )}
        </TabPanel>
      </Box>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BPlusHistoryPanel;
