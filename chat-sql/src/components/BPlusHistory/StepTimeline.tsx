/**
 * 步骤时间线组件
 * 显示会话的操作步骤历史，采用简洁的垂直直线时间轴布局，参考Git提交历史设计
 */

import React from 'react';
import {
  Box,
  Typography,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Refresh as RefreshIcon,
  FiberManualRecord as InitialIcon
} from '@mui/icons-material';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';

interface StepTimelineProps {
  session: HistorySession;
  selectedStep: HistoryStep | null;
  onStepSelect: (step: HistoryStep, stepIndex: number) => void;
}

const StepTimeline: React.FC<StepTimelineProps> = ({
  session,
  selectedStep,
  onStepSelect
}) => {
  // 获取操作图标
  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'insert':
        return <AddIcon sx={{ fontSize: 14 }} />;
      case 'delete':
        return <RemoveIcon sx={{ fontSize: 14 }} />;
      case 'reset':
        return <RefreshIcon sx={{ fontSize: 14 }} />;
      case 'initial':
        return <InitialIcon sx={{ fontSize: 14 }} />;
      default:
        return <InitialIcon sx={{ fontSize: 14 }} />;
    }
  };

  // 获取操作颜色
  const getOperationColor = (operation: string) => {
    switch (operation) {
      case 'insert':
        return '#22c55e'; // 绿色
      case 'delete':
        return '#ef4444'; // 红色
      case 'reset':
        return '#f97316'; // 橙色
      case 'initial':
        return '#3b82f6'; // 蓝色
      default:
        return '#6b7280'; // 灰色
    }
  };

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  return (
    <Paper elevation={1} sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}>
          操作历史
        </Typography>
        <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
          {session.name} - 阶数 {session.order}
        </Typography>
      </Box>

      {/* Git风格时间线 */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {session.steps.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
              暂无操作历史
            </Typography>
          </Box>
        ) : (
          <Box sx={{ position: 'relative' }}>
            {/* 主时间线 */}
            <Box
              sx={{
                position: 'absolute',
                left: 16,
                top: 0,
                bottom: 0,
                width: 2,
                backgroundColor: 'var(--card-border)',
                zIndex: 0
              }}
            />

            {session.steps.map((step, index) => {
              const isSelected = selectedStep?.id === step.id;
              const isCurrent = index === session.currentStepIndex;
              const operationColor = getOperationColor(step.operation);

              return (
                <Box
                  key={step.id}
                  onClick={() => onStepSelect(step, index)}
                  sx={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'flex-start',
                    mb: 3,
                    cursor: 'pointer',
                    p: 1,
                    borderRadius: 1,
                    backgroundColor: isSelected ? 'action.selected' : 'transparent',
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    },
                    transition: 'background-color 0.2s'
                  }}
                >
                  {/* 操作节点 */}
                  <Box
                    sx={{
                      position: 'relative',
                      zIndex: 1,
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      backgroundColor: operationColor,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      flexShrink: 0,
                      border: isCurrent ? '3px solid var(--primary-main)' : '2px solid white',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}
                  >
                    {getOperationIcon(step.operation)}
                  </Box>

                  {/* 操作信息 */}
                  <Box sx={{ ml: 2, flex: 1, minWidth: 0 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: isSelected ? 'bold' : 'medium',
                          color: 'var(--primary-text)',
                          fontSize: '0.875rem'
                        }}
                      >
                        {step.description}
                      </Typography>
                      {isCurrent && (
                        <Box
                          sx={{
                            px: 1,
                            py: 0.25,
                            backgroundColor: 'var(--primary-main)',
                            color: 'white',
                            borderRadius: '12px',
                            fontSize: '0.7rem',
                            fontWeight: 'bold'
                          }}
                        >
                          当前
                        </Box>
                      )}
                    </Box>

                    <Typography
                      variant="caption"
                      sx={{
                        color: 'var(--secondary-text)',
                        display: 'block',
                        mb: 0.5,
                        fontSize: '0.75rem'
                      }}
                    >
                      {formatTime(step.timestamp)}
                    </Typography>

                    {step.keys.length > 0 && (
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'var(--secondary-text)',
                          fontSize: '0.7rem',
                          fontFamily: 'monospace'
                        }}
                      >
                        键值: [{step.keys.join(', ')}]
                      </Typography>
                    )}
                  </Box>
                </Box>
              );
            })}
          </Box>
        )}
      </Box>

      {/* 底部统计信息 */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider', backgroundColor: 'background.paper' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
            总步骤: {session.steps.length}
          </Typography>
          <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
            当前: {session.currentStepIndex + 1}/{session.steps.length}
          </Typography>
        </Box>
        
        {selectedStep && (
          <Box sx={{ mt: 1, p: 1, backgroundColor: 'action.hover', borderRadius: 1 }}>
            <Typography variant="caption" sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}>
              选中步骤详情:
            </Typography>
            <Typography variant="caption" sx={{ color: 'var(--secondary-text)', display: 'block' }}>
              {selectedStep.description}
            </Typography>
            <Typography variant="caption" sx={{ color: 'var(--secondary-text)', display: 'block' }}>
              节点数: {selectedStep.nodes.length}, 边数: {selectedStep.edges.length}
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default StepTimeline;
