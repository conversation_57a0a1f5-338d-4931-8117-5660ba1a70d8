/**
 * 步骤时间线组件
 * 显示会话的操作步骤历史，类似Git提交历史的垂直时间线布局
 */

import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Refresh as RefreshIcon,
  FiberManualRecord as InitialIcon,
  RadioButtonChecked as ActiveIcon,
  RadioButtonUnchecked as InactiveIcon
} from '@mui/icons-material';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';

interface StepTimelineProps {
  session: HistorySession;
  selectedStep: HistoryStep | null;
  onStepSelect: (step: HistoryStep, stepIndex: number) => void;
}

const StepTimeline: React.FC<StepTimelineProps> = ({
  session,
  selectedStep,
  onStepSelect
}) => {
  // 获取操作图标
  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'insert':
        return <AddIcon sx={{ fontSize: 16 }} />;
      case 'delete':
        return <RemoveIcon sx={{ fontSize: 16 }} />;
      case 'reset':
        return <RefreshIcon sx={{ fontSize: 16 }} />;
      case 'initial':
        return <InitialIcon sx={{ fontSize: 16 }} />;
      default:
        return <InitialIcon sx={{ fontSize: 16 }} />;
    }
  };

  // 获取操作颜色
  const getOperationColor = (operation: string) => {
    switch (operation) {
      case 'insert':
        return 'success.main';
      case 'delete':
        return 'error.main';
      case 'reset':
        return 'warning.main';
      case 'initial':
        return 'info.main';
      default:
        return 'grey.500';
    }
  };

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 格式化操作描述
  const formatOperationText = (operation: string) => {
    switch (operation) {
      case 'insert':
        return '插入';
      case 'delete':
        return '删除';
      case 'reset':
        return '重置';
      case 'initial':
        return '初始化';
      default:
        return '未知';
    }
  };

  return (
    <Paper elevation={1} sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}>
          操作历史
        </Typography>
        <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
          {session.name} - 阶数 {session.order}
        </Typography>
      </Box>

      {/* 时间线列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {session.steps.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: 'var(--secondary-text)' }}>
              暂无操作历史
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {session.steps.map((step, index) => {
              const isSelected = selectedStep?.id === step.id;
              const isCurrent = index === session.currentStepIndex;
              
              return (
                <React.Fragment key={step.id}>
                  <ListItem
                    disablePadding
                    sx={{
                      borderLeft: isCurrent ? '4px solid' : '4px solid transparent',
                      borderLeftColor: isCurrent ? 'primary.main' : 'transparent',
                    }}
                  >
                    <ListItemButton
                      onClick={() => onStepSelect(step, index)}
                      sx={{
                        py: 2,
                        px: 2,
                        backgroundColor: isSelected ? 'action.selected' : 'transparent',
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        },
                        position: 'relative'
                      }}
                    >
                    {/* 时间线连接线 */}
                    {index < session.steps.length - 1 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 28,
                          top: 56,
                          bottom: -16,
                          width: 2,
                          backgroundColor: 'divider'
                        }}
                      />
                    )}

                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          backgroundColor: getOperationColor(step.operation),
                          fontSize: 14
                        }}
                      >
                        {getOperationIcon(step.operation)}
                      </Avatar>
                    </ListItemIcon>

                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: isSelected ? 'bold' : 'normal',
                              color: 'var(--primary-text)'
                            }}
                          >
                            {step.description}
                          </Typography>
                          {isCurrent && (
                            <Chip
                              label="当前"
                              size="small"
                              color="primary"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Chip
                              label={formatOperationText(step.operation)}
                              size="small"
                              variant="outlined"
                              sx={{
                                height: 18,
                                fontSize: '0.65rem',
                                color: getOperationColor(step.operation),
                                borderColor: getOperationColor(step.operation)
                              }}
                            />
                            {step.key !== undefined && (
                              <Chip
                                label={`键值: ${step.key}`}
                                size="small"
                                variant="outlined"
                                sx={{
                                  height: 18,
                                  fontSize: '0.65rem',
                                  color: 'var(--secondary-text)',
                                  borderColor: 'var(--secondary-text)'
                                }}
                              />
                            )}
                          </Box>
                          
                          <Typography
                            variant="caption"
                            sx={{ color: 'var(--secondary-text)', display: 'block' }}
                          >
                            {formatTime(step.timestamp)}
                          </Typography>
                          
                          <Typography
                            variant="caption"
                            sx={{ color: 'var(--secondary-text)', display: 'block' }}
                          >
                            键值: [{step.keys.join(', ')}]
                          </Typography>
                        </Box>
                      }
                    />

                    {/* 选中指示器 */}
                    <Box sx={{ ml: 1 }}>
                      {isSelected ? (
                        <ActiveIcon sx={{ color: 'primary.main', fontSize: 16 }} />
                      ) : (
                        <InactiveIcon sx={{ color: 'action.disabled', fontSize: 16 }} />
                      )}
                    </Box>
                    </ListItemButton>
                  </ListItem>
                  
                  {index < session.steps.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              );
            })}
          </List>
        )}
      </Box>

      {/* 底部统计信息 */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider', backgroundColor: 'background.paper' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
            总步骤: {session.steps.length}
          </Typography>
          <Typography variant="caption" sx={{ color: 'var(--secondary-text)' }}>
            当前: {session.currentStepIndex + 1}/{session.steps.length}
          </Typography>
        </Box>
        
        {selectedStep && (
          <Box sx={{ mt: 1, p: 1, backgroundColor: 'action.hover', borderRadius: 1 }}>
            <Typography variant="caption" sx={{ color: 'var(--primary-text)', fontWeight: 'bold' }}>
              选中步骤详情:
            </Typography>
            <Typography variant="caption" sx={{ color: 'var(--secondary-text)', display: 'block' }}>
              {selectedStep.description}
            </Typography>
            <Typography variant="caption" sx={{ color: 'var(--secondary-text)', display: 'block' }}>
              节点数: {selectedStep.nodes.length}, 边数: {selectedStep.edges.length}
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default StepTimeline;
