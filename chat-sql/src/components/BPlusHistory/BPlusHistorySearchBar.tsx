/**
 * B+树历史管理搜索栏组件
 * 参考主页面搜索记录功能的UI设计，提供搜索和新建会话功能
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  TextField,
  Button,
  Tooltip,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { getBPlusHistoryStorage } from '@/lib/bplus-tree/historyStorage';
import { HistorySession } from '@/types/bPlusHistory';

interface BPlusHistorySearchBarProps {
  onSearch: (query: string) => void;
  onSessionCreated: (session: HistorySession) => void;
  searchQuery: string;
}

const BPlusHistorySearchBar: React.FC<BPlusHistorySearchBarProps> = ({
  onSearch,
  onSessionCreated,
  searchQuery
}) => {
  const [isNewSessionDialogOpen, setIsNewSessionDialogOpen] = useState(false);
  const [newSessionName, setNewSessionName] = useState('');
  const [newSessionOrder, setNewSessionOrder] = useState(3);
  const [isCreating, setIsCreating] = useState(false);

  // 处理搜索输入
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(event.target.value);
  }, [onSearch]);

  // 清除搜索
  const handleClearSearch = useCallback(() => {
    onSearch('');
  }, [onSearch]);

  // 打开新建会话对话框
  const handleOpenNewSessionDialog = useCallback(() => {
    setNewSessionName('');
    setNewSessionOrder(3);
    setIsNewSessionDialogOpen(true);
  }, []);

  // 关闭新建会话对话框
  const handleCloseNewSessionDialog = useCallback(() => {
    setIsNewSessionDialogOpen(false);
    setNewSessionName('');
    setNewSessionOrder(3);
  }, []);

  // 创建新会话
  const handleCreateSession = useCallback(async () => {
    if (!newSessionName.trim()) {
      return;
    }

    setIsCreating(true);
    try {
      const storage = await getBPlusHistoryStorage();
      const result = await storage.createSession(newSessionName.trim(), newSessionOrder);
      
      if (result.success) {
        onSessionCreated(result.data);
        handleCloseNewSessionDialog();
      } else {
        console.error('创建会话失败:', result.error);
      }
    } catch (error) {
      console.error('创建会话时发生错误:', error);
    } finally {
      setIsCreating(false);
    }
  }, [newSessionName, newSessionOrder, onSessionCreated, handleCloseNewSessionDialog]);

  return (
    <>
      <Box sx={{
        p: 2,
        borderBottom: '1px solid var(--card-border)',
        backgroundColor: 'var(--card-bg)',
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        {/* 搜索框 */}
        <TextField
          size="small"
          placeholder="搜索会话和操作记录"
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{
            flex: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: '20px',
              backgroundColor: 'var(--input-bg)',
              color: 'var(--input-text)',
              fontSize: '0.875rem',
              '& fieldset': {
                borderColor: 'var(--input-border)',
              },
              '&:hover fieldset': {
                borderColor: 'var(--link-color)',
              },
              '&.Mui-focused fieldset': {
                borderColor: 'var(--link-color)',
                boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
              },
            },
            '& .MuiInputBase-input::placeholder': {
              color: 'var(--tertiary-text)',
              opacity: 1,
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ 
                  color: 'var(--icon-color)', 
                  fontSize: '1.1rem' 
                }} />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <Tooltip title="清除搜索">
                  <Button
                    size="small"
                    onClick={handleClearSearch}
                    sx={{
                      minWidth: 'auto',
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      color: 'var(--icon-color)',
                      '&:hover': {
                        backgroundColor: 'var(--button-hover)',
                        color: 'var(--icon-color-hover)',
                      }
                    }}
                  >
                    <ClearIcon sx={{ fontSize: '1rem' }} />
                  </Button>
                </Tooltip>
              </InputAdornment>
            ),
          }}
        />

        {/* 新建会话按钮 */}
        <Tooltip title="新建B+树会话">
          <Button
            variant="contained"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleOpenNewSessionDialog}
            sx={{
              borderRadius: '20px',
              backgroundColor: 'var(--link-color)',
              color: 'white',
              fontSize: '0.875rem',
              fontWeight: 'medium',
              textTransform: 'none',
              px: 2,
              '&:hover': {
                backgroundColor: 'var(--link-hover)',
              },
            }}
          >
            新建
          </Button>
        </Tooltip>
      </Box>

      {/* 新建会话对话框 */}
      <Dialog
        open={isNewSessionDialogOpen}
        onClose={handleCloseNewSessionDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            backgroundColor: 'var(--card-bg)',
          }
        }}
      >
        <DialogTitle sx={{ 
          color: 'var(--primary-text)',
          fontWeight: 'bold'
        }}>
          新建B+树会话
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              autoFocus
              label="会话名称"
              fullWidth
              value={newSessionName}
              onChange={(e) => setNewSessionName(e.target.value)}
              placeholder="请输入会话名称"
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'var(--input-bg)',
                  color: 'var(--input-text)',
                },
                '& .MuiInputLabel-root': {
                  color: 'var(--secondary-text)',
                },
              }}
            />
            <FormControl fullWidth>
              <InputLabel sx={{ color: 'var(--secondary-text)' }}>
                B+树阶数
              </InputLabel>
              <Select
                value={newSessionOrder}
                label="B+树阶数"
                onChange={(e) => setNewSessionOrder(e.target.value as number)}
                sx={{
                  backgroundColor: 'var(--input-bg)',
                  color: 'var(--input-text)',
                }}
              >
                <MenuItem value={3}>3阶</MenuItem>
                <MenuItem value={4}>4阶</MenuItem>
                <MenuItem value={5}>5阶</MenuItem>
                <MenuItem value={6}>6阶</MenuItem>
                <MenuItem value={7}>7阶</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button 
            onClick={handleCloseNewSessionDialog}
            sx={{ 
              color: 'var(--secondary-text)',
              textTransform: 'none'
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleCreateSession}
            variant="contained"
            disabled={!newSessionName.trim() || isCreating}
            sx={{
              backgroundColor: 'var(--link-color)',
              color: 'white',
              textTransform: 'none',
              '&:hover': {
                backgroundColor: 'var(--link-hover)',
              },
            }}
          >
            {isCreating ? '创建中...' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BPlusHistorySearchBar;
